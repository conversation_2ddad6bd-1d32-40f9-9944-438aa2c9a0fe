using UnityEngine;
using System.Collections.Generic;
using Mirror;

public class ForestPlayerManager : MonoBehaviour
{
    [Header("Prefabs")]
    [SerializeField] private GameObject clientPlayerPrefab;
    [SerializeField] private GameObject replicatedPlayerPrefabOne;
    [SerializeField] private GameObject replicatedPlayerPrefabTwo;
    [SerializeField] private GameObject replicatedPlayerPrefabThree;
    [Header("Scene References")]
    [SerializeField] private Camera playerCameraToAssign;

    [Header("Spawn Points")]
    [SerializeField] private Transform localPlayerSpawnPoint;
    [SerializeField] private List<Transform> replicatedPlayerSpawnPoints = new List<Transform>();

    private ForestIntroPlayer _localPlayerInstance;
    private List<ForestIntroReplicatedPlayer> _replicatedPlayerInstances = new List<ForestIntroReplicatedPlayer>();
    private List<GameObject> _networkedPlayers = new List<GameObject>();
    private bool _hasSpawnedNetworkedPlayers = false;

    void Start()
    {
        // Only spawn non-networked intro players if we're not in multiplayer mode
        if (!MyNetworkManager.isMultiplayer)
        {
            SpawnLocalPlayer();
            SpawnReplicatedPlayers();
        }
        else
        {
            // In multiplayer, wait for network spawning to complete
        }
    }

    void SpawnLocalPlayer()
    {
        if (clientPlayerPrefab == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'clientPlayerPrefab' is not assigned.", this);
            return;
        }
        if (localPlayerSpawnPoint == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'localPlayerSpawnPoint' is not assigned. Cannot spawn local player.", this);
            return;
        }

        // Spawn without parenting to avoid transform hierarchy issues (consistent with networked player spawning)
        GameObject localPlayerGO = Instantiate(clientPlayerPrefab, localPlayerSpawnPoint.position, localPlayerSpawnPoint.rotation);
        _localPlayerInstance = localPlayerGO.GetComponent<ForestIntroPlayer>();

        if (_localPlayerInstance == null)
        {
            Debug.LogError("ForestIntroPlayerManager: The 'clientPlayerPrefab' does not have a ForestIntroPlayer component.", localPlayerGO);
            Destroy(localPlayerGO); // Cleanup
        }
        else
        {
            localPlayerGO.name = "Local_ForestIntroPlayer";

            // Set up transform hierarchy after instantiation (consistent with networked players)
            SetupLocalPlayerTransform(localPlayerGO, localPlayerSpawnPoint);

            // Add HelicopterTransformConstraint component for helicopter flight constraints (consistent with networked players)
            HelicopterTransformConstraint constraint = localPlayerGO.GetComponent<HelicopterTransformConstraint>();
            if (constraint == null)
            {
                constraint = localPlayerGO.AddComponent<HelicopterTransformConstraint>();
                Debug.Log($"ForestPlayerManager: Added HelicopterTransformConstraint to {localPlayerGO.name}");
            }

            if (playerCameraToAssign != null)
            {
                _localPlayerInstance.InitializePlayerCamera(playerCameraToAssign);
            }
            else
            {
                _localPlayerInstance.InitializePlayerCamera(null); // Call with null to allow fallback in player script
            }
        }
    }

    private void SetupLocalPlayerTransform(GameObject localPlayer, Transform spawnPoint)
    {
        if (localPlayer != null && spawnPoint != null)
        {
            Debug.Log($"ForestPlayerManager: Setting up local player transform at spawn point '{spawnPoint.name}'");
            Debug.Log($"ForestPlayerManager: Before transform setup - Player world pos: {localPlayer.transform.position}, rot: {localPlayer.transform.rotation.eulerAngles}");

            // Parent to spawn point and reset local transform to ensure correct local coordinates
            localPlayer.transform.SetParent(spawnPoint);
            localPlayer.transform.localPosition = Vector3.zero;
            localPlayer.transform.localRotation = Quaternion.identity;

            Debug.Log($"ForestPlayerManager: After transform setup - Player local pos: {localPlayer.transform.localPosition}, rot: {localPlayer.transform.localRotation.eulerAngles}");
            Debug.Log($"ForestPlayerManager: Player world pos: {localPlayer.transform.position}, rot: {localPlayer.transform.rotation.eulerAngles}");
        }
        else
        {
            Debug.LogError($"ForestPlayerManager: Cannot setup local player transform - localPlayer or spawnPoint is null");
        }
    }

    void SpawnReplicatedPlayers()
    {
        List<GameObject> availableReplicatedPrefabs = new List<GameObject>();
        if (replicatedPlayerPrefabOne != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabOne);
        if (replicatedPlayerPrefabTwo != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabTwo);
        if (replicatedPlayerPrefabThree != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabThree);

        if (availableReplicatedPrefabs.Count == 0)
        {
            Debug.LogError("ForestPlayerManager: No replicated player prefabs assigned (One, Two, or Three). Cannot spawn replicated players.", this);
            return;
        }

        if (replicatedPlayerSpawnPoints.Count == 0)
        {
            return;
        }

        int numberOfPlayersToSpawn = Mathf.Min(replicatedPlayerSpawnPoints.Count, availableReplicatedPrefabs.Count);



        for (int i = 0; i < numberOfPlayersToSpawn; i++)
        {
            Transform spawnPoint = replicatedPlayerSpawnPoints[i];
            GameObject prefabToSpawn = availableReplicatedPrefabs[i];

            if (spawnPoint == null)
            {
                continue;
            }

            if (prefabToSpawn == null)
            {
                continue;
            }

            // Player 1 (Host) - no special rotation here.
            // Player 2 (Replicated Index 0) - spawns with default spawn point rotation.
            // Player 3 (Replicated Index 1) - needs 180 Y rotation.
            // Player 4 (Replicated Index 2) - needs 180 Y rotation.
            // Instantiate with spawn point's initial rotation first.
            GameObject replicatedPlayerGO = Instantiate(prefabToSpawn, spawnPoint.position, spawnPoint.rotation, spawnPoint);
            ForestIntroReplicatedPlayer replicatedPlayer = replicatedPlayerGO.GetComponent<ForestIntroReplicatedPlayer>();

            if (replicatedPlayer == null)
            {
                Debug.LogError($"ForestPlayerManager: The prefab \"{prefabToSpawn.name}\" does not have a ForestIntroReplicatedPlayer component. Spawned for index {i}.", replicatedPlayerGO);
                Destroy(replicatedPlayerGO); // Cleanup
            }
            else
            {
                replicatedPlayerGO.name = $"Replicated_ForestIntroPlayer_{i}_{prefabToSpawn.name}";
                _replicatedPlayerInstances.Add(replicatedPlayer);

                // Set initial Y rotation offset for Player 3 (index 1) and Player 4 (index 2)
                if (i == 1 || i == 2)
                {
                    replicatedPlayer.SetInitialYRotationOffset(180f);
                }
            }
        }
        
        if (clientPlayerPrefab != null && clientPlayerPrefab.GetComponent<ForestIntroPlayer>() != null)
        {
            clientPlayerPrefab.GetComponent<ForestIntroPlayer>().LockCursor();
        }
    }

    /// <summary>
    /// Called by MyNetworkManager to spawn a networked player in the forest scene
    /// </summary>
    public void SpawnNetworkedPlayer(NetworkConnectionToClient conn, GameObject networkPlayerPrefab)
    {
        if (networkPlayerPrefab == null)
        {
            Debug.LogError("ForestPlayerManager: Network player prefab is null.");
            return;
        }

        // Determine spawn point based on connection
        Transform spawnPoint = GetSpawnPointForConnection(conn);

        if (spawnPoint == null)
        {
            Debug.LogError("ForestPlayerManager: No available spawn point for networked player.");
            return;
        }

        // Debug spawn point hierarchy
        Debug.Log($"ForestPlayerManager: Using spawn point '{spawnPoint.name}' for connection {conn.connectionId}");
        Debug.Log($"ForestPlayerManager: Spawn point hierarchy: {GetTransformHierarchy(spawnPoint)}");

        // Spawn the networked player at spawn point position and rotation to avoid Mirror overriding transforms
        GameObject networkedPlayer = Instantiate(networkPlayerPrefab, spawnPoint.position, spawnPoint.rotation);

        Debug.Log($"ForestPlayerManager: Spawned player at spawn point world pos: {networkedPlayer.transform.position}, rot: {networkedPlayer.transform.rotation.eulerAngles}");
        Debug.Log($"ForestPlayerManager: Spawn point '{spawnPoint.name}' world pos: {spawnPoint.position}, rot: {spawnPoint.rotation.eulerAngles}");

        // Configure the networked player for the forest intro BEFORE network registration
        ConfigureNetworkedPlayerForIntro(networkedPlayer, conn, spawnPoint);

        // Register with Mirror networking
        NetworkServer.AddPlayerForConnection(conn, networkedPlayer);

        // Track the spawned player
        _networkedPlayers.Add(networkedPlayer);

        // Initialize the player through the network manager
        MyNetworkManager.instance.InitializeSpawnedPlayer(conn, networkedPlayer);

        // Apply proper parenting and transform setup AFTER Mirror networking initialization
        StartCoroutine(SetupPlayerTransformAfterNetworkInit(networkedPlayer, spawnPoint, conn));

        Debug.Log($"ForestPlayerManager: Successfully spawned networked player '{networkedPlayer.name}' at {spawnPoint.name}");

        // Mark that we've started spawning networked players
        _hasSpawnedNetworkedPlayers = true;
    }

    private Transform GetSpawnPointForConnection(NetworkConnectionToClient conn)
    {
        // For the local server connection (host), use the local player spawn point
        if (conn == NetworkServer.localConnection)
        {
            return localPlayerSpawnPoint;
        }

        // For remote connections, use replicated player spawn points
        int connectionIndex = GetConnectionIndex(conn);

        if (connectionIndex >= 0 && connectionIndex < replicatedPlayerSpawnPoints.Count)
        {
            return replicatedPlayerSpawnPoints[connectionIndex];
        }

        // Fallback to local spawn point if no specific point available
        Debug.LogWarning($"ForestPlayerManager: No specific spawn point for connection {conn.connectionId}, using local spawn point.");
        return localPlayerSpawnPoint;
    }

    private int GetConnectionIndex(NetworkConnectionToClient conn)
    {
        // Simple index assignment based on connection ID
        // In a more sophisticated system, you might track this differently
        return conn.connectionId - 1; // Subtract 1 because host is connection 0
    }

    private void ConfigureNetworkedPlayerForIntro(GameObject networkedPlayer, NetworkConnectionToClient conn, Transform spawnPoint)
    {
        // Ensure the networked player has the necessary components for the intro
        MyClient myClient = networkedPlayer.GetComponent<MyClient>();
        if (myClient == null)
        {
            Debug.LogError("ForestPlayerManager: Networked player missing MyClient component.");
            return;
        }

        // Add ForestIntroPlayer component if not present
        ForestIntroPlayer introPlayer = networkedPlayer.GetComponent<ForestIntroPlayer>();
        if (introPlayer == null)
        {
            introPlayer = networkedPlayer.AddComponent<ForestIntroPlayer>();
        }

        // Add HelicopterTransformConstraint component for helicopter flight constraints
        HelicopterTransformConstraint constraint = networkedPlayer.GetComponent<HelicopterTransformConstraint>();
        if (constraint == null)
        {
            constraint = networkedPlayer.AddComponent<HelicopterTransformConstraint>();
            Debug.Log($"ForestPlayerManager: Added HelicopterTransformConstraint to {networkedPlayer.name}");
        }

        // Store spawn point reference for constraint system
        if (constraint != null)
        {
            // The constraint will get the spawn point from transform.parent after parenting is complete
            Debug.Log($"ForestPlayerManager: HelicopterTransformConstraint added to {networkedPlayer.name}, will be activated after transform setup");
        }

        // Wait longer for proper NetworkBehaviour initialization before activating constraints
        if (NetworkServer.active && constraint != null)
        {
            StartCoroutine(ActivateConstraintsAfterDelay(constraint));
        }

        // Configure camera for local player
        if (conn == NetworkServer.localConnection && playerCameraToAssign != null)
        {
            introPlayer.InitializePlayerCamera(playerCameraToAssign);
            _localPlayerInstance = introPlayer;
        }

        // Set the player's name for identification
        networkedPlayer.name = conn == NetworkServer.localConnection ? "NetworkedHost_ForestPlayer" : $"NetworkedClient_ForestPlayer_{conn.connectionId}";

        // Note: Local transform will be set to (0,0,0) after network initialization in SetupPlayerTransformAfterNetworkInit
    }

    private System.Collections.IEnumerator SetupPlayerTransformAfterNetworkInit(GameObject networkedPlayer, Transform spawnPoint, NetworkConnectionToClient conn)
    {
        // Wait for Mirror networking to complete initialization
        yield return null;
        yield return null;

        // Additional wait to ensure network state is stable
        yield return new WaitForSeconds(0.05f);

        if (networkedPlayer != null && spawnPoint != null)
        {
            Debug.Log($"ForestPlayerManager: Setting up transform for {networkedPlayer.name} after network init");
            Debug.Log($"ForestPlayerManager: Before transform setup - Player world pos: {networkedPlayer.transform.position}, rot: {networkedPlayer.transform.rotation.eulerAngles}");

            // Parent to spawn point and reset local transform to ensure correct local coordinates
            networkedPlayer.transform.SetParent(spawnPoint);
            networkedPlayer.transform.localPosition = Vector3.zero;
            networkedPlayer.transform.localRotation = Quaternion.identity;

            Debug.Log($"ForestPlayerManager: After transform setup - Player local pos: {networkedPlayer.transform.localPosition}, rot: {networkedPlayer.transform.localRotation.eulerAngles}");
            Debug.Log($"ForestPlayerManager: Player world pos: {networkedPlayer.transform.position}, rot: {networkedPlayer.transform.rotation.eulerAngles}");
        }
        else
        {
            Debug.LogError($"ForestPlayerManager: Cannot setup transform - networkedPlayer or spawnPoint is null");
        }
    }

    private System.Collections.IEnumerator ActivateConstraintsAfterDelay(HelicopterTransformConstraint constraint)
    {
        // Wait longer to ensure NetworkBehaviour and all components are properly initialized
        yield return null;
        yield return null;
        yield return null;
        yield return null;

        // Additional wait for network initialization and transform setup to complete
        yield return new WaitForSeconds(0.15f);

        if (constraint != null)
        {
            // Verify the constraint is properly initialized before activation
            if (constraint.HasValidSpawnPoint)
            {
                Debug.Log($"ForestPlayerManager: Activating constraints for {constraint.gameObject.name} after initialization delay");
                constraint.ActivateConstraints();
            }
            else
            {
                Debug.LogWarning($"ForestPlayerManager: Cannot activate constraints for {constraint.gameObject.name} - spawn point not valid. " +
                               "Retrying in 0.2 seconds...");

                // Retry once more after additional delay
                yield return new WaitForSeconds(0.2f);

                if (constraint != null && constraint.HasValidSpawnPoint)
                {
                    Debug.Log($"ForestPlayerManager: Activating constraints for {constraint.gameObject.name} after retry");
                    constraint.ActivateConstraints();
                }
                else
                {
                    Debug.LogError($"ForestPlayerManager: Failed to activate constraints for {constraint?.gameObject?.name} - spawn point still not valid after retry");
                }
            }
        }
    }

    private string GetTransformHierarchy(Transform t)
    {
        if (t == null) return "null";

        string hierarchy = t.name;
        Transform parent = t.parent;

        while (parent != null)
        {
            hierarchy = parent.name + "/" + hierarchy;
            parent = parent.parent;
        }

        return hierarchy;
    }

    void Update()
    {
        // Handle both networked and non-networked scenarios
        if (MyNetworkManager.isMultiplayer)
        {
            HandleNetworkedPlayerUpdates();
        }
        else
        {
            HandleLocalPlayerUpdates();
        }
    }

    private void HandleNetworkedPlayerUpdates()
    {
        // In multiplayer, the actual player synchronization is handled by Mirror
        // We only need to manage local intro-specific behavior

        if (_localPlayerInstance != null && _networkedPlayers.Count > 1)
        {
            // Get local player orientation for any local intro effects
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldYRotation = localPlayerOrientation.eulerAngles.y;

            // Note: In a real networked scenario, this data would be synchronized
            // through Mirror's networking system rather than direct method calls
        }
    }

    private void HandleLocalPlayerUpdates()
    {
        // This section simulates network updates for single-player mode.
        // In single-player, we simulate multiplayer behavior with replicated players.

        if (_localPlayerInstance != null && _replicatedPlayerInstances.Count > 0)
        {
            // 1. Get the Y rotation (yaw) from the local client player.
            // Using GetWorldOrientation() and then taking .eulerAngles.y to get the yaw component.
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldYRotation = localPlayerOrientation.eulerAngles.y;

            // 2. "Send" this Y rotation to all replicated players.
            foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)
            {
                if (replicatedPlayer != null)
                {
                    // Here we directly call the method that uses the Y rotation.
                    // The Replicated Player script itself ensures only its Y world axis is changed.
                    replicatedPlayer.SetVisualWorldYRotation(localPlayerWorldYRotation);
                }
            }
        }
    }

    /// <summary>
    /// Clean up spawned players when the scene is unloaded
    /// </summary>
    private void OnDestroy()
    {
        // Clean up non-networked players
        if (_localPlayerInstance != null && !MyNetworkManager.isMultiplayer)
        {
            Destroy(_localPlayerInstance.gameObject);
        }

        foreach (var replicatedPlayer in _replicatedPlayerInstances)
        {
            if (replicatedPlayer != null && !MyNetworkManager.isMultiplayer)
            {
                Destroy(replicatedPlayer.gameObject);
            }
        }

        // Clear lists
        _replicatedPlayerInstances.Clear();
        _networkedPlayers.Clear();
    }

    /// <summary>
    /// Get the current local player instance (networked or non-networked)
    /// </summary>
    public ForestIntroPlayer GetLocalPlayerInstance()
    {
        return _localPlayerInstance;
    }

    /// <summary>
    /// Check if the manager has spawned any players
    /// </summary>
    public bool HasSpawnedPlayers()
    {
        return _localPlayerInstance != null || _networkedPlayers.Count > 0;
    }

    /// <summary>
    /// Get count of active players in the scene
    /// </summary>
    public int GetActivePlayerCount()
    {
        if (MyNetworkManager.isMultiplayer)
        {
            return _networkedPlayers.Count;
        }
        else
        {
            int count = 0;
            if (_localPlayerInstance != null) count++;
            count += _replicatedPlayerInstances.Count;
            return count;
        }
    }
}
