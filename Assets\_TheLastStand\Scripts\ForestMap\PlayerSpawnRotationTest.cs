using UnityEngine;
using System.Collections;

/// <summary>
/// Test script to verify player spawning rotation fixes
/// Attach to ForestPlayerManager to run automated rotation tests
/// </summary>
public class PlayerSpawnRotationTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestsOnStart = false;
    [SerializeField] private bool enableDebugLogs = true;
    
    private ForestPlayerManager playerManager;
    
    void Start()
    {
        playerManager = GetComponent<ForestPlayerManager>();
        
        if (runTestsOnStart && playerManager != null)
        {
            StartCoroutine(RunRotationTests());
        }
    }
    
    private IEnumerator RunRotationTests()
    {
        yield return new WaitForSeconds(2f); // Wait for initial spawning to complete
        
        LogTest("=== PLAYER SPAWN ROTATION VERIFICATION TESTS ===");
        
        // Test 1: Verify local player rotation
        yield return StartCoroutine(TestLocalPlayerRotation());
        
        // Test 2: Verify replicated player rotations
        yield return StartCoroutine(TestReplicatedPlayerRotations());
        
        // Test 3: Verify networked player rotation (if in multiplayer)
        if (MyNetworkManager.isMultiplayer)
        {
            yield return StartCoroutine(TestNetworkedPlayerRotation());
        }
        
        LogTest("=== ROTATION TESTS COMPLETED ===");
    }
    
    private IEnumerator TestLocalPlayerRotation()
    {
        LogTest("--- Testing Local Player Rotation ---");
        
        ForestIntroPlayer localPlayer = playerManager.GetLocalPlayerInstance();
        if (localPlayer != null)
        {
            Vector3 initialRotation = localPlayer.transform.rotation.eulerAngles;
            LogTest($"Local player initial rotation: {initialRotation}");
            
            // Check if player is upright (X and Z should be close to 0)
            bool isUpright = Mathf.Abs(initialRotation.x) < 5f && Mathf.Abs(initialRotation.z) < 5f;
            LogTest($"Local player upright check: {(isUpright ? "PASS" : "FAIL")} (X: {initialRotation.x:F2}, Z: {initialRotation.z:F2})");
            
            // Test player model rotation if available
            MyClient myClient = localPlayer.GetComponent<MyClient>();
            if (myClient != null && myClient.MeshObj != null)
            {
                Vector3 modelRotation = myClient.MeshObj.transform.rotation.eulerAngles;
                LogTest($"Local player model rotation: {modelRotation}");
                
                bool modelUpright = Mathf.Abs(modelRotation.x) < 5f && Mathf.Abs(modelRotation.z) < 5f;
                LogTest($"Local player model upright check: {(modelUpright ? "PASS" : "FAIL")} (X: {modelRotation.x:F2}, Z: {modelRotation.z:F2})");
            }
        }
        else
        {
            LogTest("Local player not found - SKIP");
        }
        
        yield return null;
    }
    
    private IEnumerator TestReplicatedPlayerRotations()
    {
        LogTest("--- Testing Replicated Player Rotations ---");
        
        ForestIntroReplicatedPlayer[] replicatedPlayers = FindObjectsOfType<ForestIntroReplicatedPlayer>();
        
        if (replicatedPlayers.Length > 0)
        {
            for (int i = 0; i < replicatedPlayers.Length; i++)
            {
                ForestIntroReplicatedPlayer player = replicatedPlayers[i];
                Vector3 playerRotation = player.transform.rotation.eulerAngles;
                LogTest($"Replicated player {i} rotation: {playerRotation}");
                
                bool isUpright = Mathf.Abs(playerRotation.x) < 5f && Mathf.Abs(playerRotation.z) < 5f;
                LogTest($"Replicated player {i} upright check: {(isUpright ? "PASS" : "FAIL")} (X: {playerRotation.x:F2}, Z: {playerRotation.z:F2})");
            }
        }
        else
        {
            LogTest("No replicated players found - SKIP");
        }
        
        yield return null;
    }
    
    private IEnumerator TestNetworkedPlayerRotation()
    {
        LogTest("--- Testing Networked Player Rotations ---");
        
        MyClient[] networkedClients = FindObjectsOfType<MyClient>();
        
        if (networkedClients.Length > 0)
        {
            for (int i = 0; i < networkedClients.Length; i++)
            {
                MyClient client = networkedClients[i];
                Vector3 clientRotation = client.transform.rotation.eulerAngles;
                LogTest($"Networked client {i} rotation: {clientRotation}");
                
                bool isUpright = Mathf.Abs(clientRotation.x) < 5f && Mathf.Abs(clientRotation.z) < 5f;
                LogTest($"Networked client {i} upright check: {(isUpright ? "PASS" : "FAIL")} (X: {clientRotation.x:F2}, Z: {clientRotation.z:F2})");
                
                // Test mesh object rotation
                if (client.MeshObj != null)
                {
                    Vector3 meshRotation = client.MeshObj.transform.rotation.eulerAngles;
                    LogTest($"Networked client {i} mesh rotation: {meshRotation}");
                    
                    bool meshUpright = Mathf.Abs(meshRotation.x) < 5f && Mathf.Abs(meshRotation.z) < 5f;
                    LogTest($"Networked client {i} mesh upright check: {(meshUpright ? "PASS" : "FAIL")} (X: {meshRotation.x:F2}, Z: {meshRotation.z:F2})");
                }
            }
        }
        else
        {
            LogTest("No networked clients found - SKIP");
        }
        
        yield return null;
    }
    
    private void LogTest(string message)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[PlayerSpawnRotationTest] {message}");
        }
    }
    
    /// <summary>
    /// Manual test trigger for runtime testing
    /// </summary>
    [ContextMenu("Run Rotation Tests")]
    public void RunTestsManually()
    {
        if (playerManager == null)
            playerManager = GetComponent<ForestPlayerManager>();
            
        StartCoroutine(RunRotationTests());
    }
}
